# Project层级权限改造完整方案

## 1. 方案分析与验证

### 1.1 你的方案分析

你提出的四个改造点都是**正确且必要的**：

✅ **1. Keto静态规则添加** - 必须，定义Project权限模型
✅ **2. IAM动态规则改造** - 必须，实现Project权限管理
✅ **3. Anno权限检查兼容** - 必须，保证历史数据可访问
✅ **4. Project业务逻辑改造** - 必须，实现完整的CRUD权限

### 1.2 补充要点

**需要额外考虑的关键点：**

🔸 **权限继承链修改**: Lot的父级关系需要支持Project
🔸 **命名空间更新**: AnnoLot命名空间需要支持AnnoProject父级
🔸 **权限常量定义**: 需要添加Project相关的权限常量
🔸 **双轨制实现**: 确保新老逻辑并存且互不干扰

## 2. 完整改造方案

### 2.1 阶段一：Keto静态规则改造

#### 文件：`keto/tuples.txt`

**需要添加的内容：**

```plaintext
# ==================== AnnoProject 权限定义 ====================

# AnnoProject 基础角色权限
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>
IamRole:AnnoProject.viewer#<EMAIL>

IamRole:AnnoProject.editor#perms@IamRole:AnnoProject.viewer#perms
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>
IamRole:AnnoProject.editor#<EMAIL>

IamRole:AnnoProject.owner#perms@IamRole:AnnoProject.editor#perms
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#<EMAIL>
IamRole:AnnoProject.owner#<EMAIL>

# Project 权限继承到 Lot (关键!)
IamRole:AnnoProject.owner#perms@IamRole:AnnoLot.owner#perms
IamRole:AnnoProject.editor#perms@IamRole:AnnoLot.editor#perms
IamRole:AnnoProject.viewer#perms@IamRole:AnnoLot.viewer#perms

# 复合角色集成 Project 权限
IamRole:anno.owner#perms@IamRole:AnnoProject.owner#perms
IamRole:anno.editor#perms@IamRole:AnnoProject.editor#perms
IamRole:anno.viewer#perms@IamRole:AnnoProject.viewer#perms

# KAM 和 PM 角色集成 Project 权限
IamRole:anno.kam#perms@IamRole:AnnoProject.owner#perms
IamRole:anno.pm#perms@IamRole:AnnoProject.editor#perms

# 全局策略应用到 AnnoProject
AnnoProject:*#policies@IamPolicy:sys/admin
AnnoProject:*#policies@IamPolicy:sys/inspector
AnnoProject:*#policies@IamPolicy:sys/kam
AnnoProject:*#policies@IamPolicy:sys/pm

# AnnoProject 受全局策略控制
IamRole:AnnoProject.viewer#policies@IamPolicy:sys/admin
IamRole:AnnoProject.viewer#policies@IamPolicy:sys/publicView
IamRole:AnnoProject.editor#policies@IamPolicy:sys/admin
IamRole:AnnoProject.editor#policies@IamPolicy:sys/publicView
IamRole:AnnoProject.owner#policies@IamPolicy:sys/admin
IamRole:AnnoProject.owner#policies@IamPolicy:sys/publicView
```

#### 文件：`keto/namespaces.keto.ts`

**需要修改的内容：**

1. **AnnoProject命名空间已存在，需要验证是否正确**
2. **AnnoLot命名空间需要修改，支持AnnoProject父级**

```typescript
// 验证 AnnoProject 命名空间 (已存在，检查是否正确)
class AnnoProject implements Namespace {
  related: {
    parents: IamGroup[] // 项目所属的组织
    policies: IamPolicy[]
    lots: AnnoLot[] // 项目下的批次 (可选，用于反向查询)
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoProject:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),

    get: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.get"),
    list: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.list"),
    update: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.update"),
    delete: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.delete"),
    create: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.create"),
    getPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.getPolicy"),
    setPolicy: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.setPolicy"),

    // 项目特有权限
    createLot: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.createLot"),
    listLot: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.listLot"),
    manageMember: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.manageMember"),
    stat: (ctx: Context): boolean => this.permits.check(ctx, "AnnoProject.stat"),
  }
}

// 修改 AnnoLot 命名空间，支持 AnnoProject 父级
class AnnoLot implements Namespace {
  related: {
    parents: (IamGroup|AnnoLot|AnnoProject)[] // 添加 AnnoProject 作为父级
    policies: IamPolicy[]
  }

  permits = {
    check: (ctx: Context, perm: string): boolean =>
      AnnoLot:"*".permits.check(ctx, perm) ||
      this.related.parents.traverse((p) => p.permits.check(ctx, perm)) ||
      this.related.policies.traverse((p) => p.permits.check(ctx, perm)),
    // ... 其他权限方法保持不变
  }
}
```

### 2.2 阶段二：IAM动态规则改造

#### 文件：`iam/pkg/keto/object.go`

**方案选择：在object.go中添加Project支持**

**原因：**
- Project本质上是一种业务资源，符合Object规则的通用模式
- 可以复用现有的资源权限管理框架
- 保持代码结构的一致性

**需要添加的内容：**

```go
// 在现有常量中添加
const (
    // 现有常量保持不变...
    
    // Project 相关常量
    ProjectNs = "AnnoProject"
)

// 在现有模板中添加 Project 支持
// resourceTpl, resourceOwnerUserTpl, resourceOwnerGroupTpl, resourceParentTpl 
// 这些模板已经是通用的，支持任意命名空间，无需修改

// 添加 Project 特定的辅助函数
func MakeProjectName(uid string) string { 
    return MakeObjectName(ProjectNs, uid) 
}

func ProjectScope(uid string) string { 
    return MakeObjectName(ProjectNs, uid) 
}

// 添加 Project 权限策略创建方法
func (o *AccessMgr) CreateProjectAccessPolicies(ctx context.Context, projectUid string, owners []string, parents []string) error {
    objects := []string{MakeProjectName(projectUid)}
    return o.CreateAccessPolicies(ctx, objects, owners, parents)
}
```

**不需要单独的project.go文件**，因为：
- Object.go已经提供了通用的资源权限管理框架
- Project的权限模式与其他资源（Lot、Job、Order）完全一致
- 保持代码结构简洁统一

### 2.3 阶段三：Anno权限常量定义

#### 文件：`anno/internal/biz/perms.go`

**需要添加的内容：**

```go
const (
    // 现有权限保持不变...
    
    // Project 权限
    PermCreateLot     = "createLot"
    PermListLot       = "listLot"
    PermManageMember  = "manageMember"
)

const (
    // 现有权限类保持不变...
    
    // Project 权限类
    PermClsProject = "AnnoProject"
)

// Project 作用域函数
func ProjectScope(uid string) string { 
    return ResourceScope(PermClsProject, uid) 
}
```

### 2.4 阶段四：Anno权限检查兼容改造

#### 文件：`anno/internal/service/lot.go`

**CreateLot方法改造：**

```go
func (o *LotsService) CreateLot(ctx context.Context, req *anno.CreateLotRequest) (*anno.Lot, error) {
    // ... 现有逻辑保持不变 ...
    
    user := biz.UserFromCtx(ctx)
    scope := req.OrgUid
    if scope == "" {
        scope = user.GetOrgUid()
        req.OrgUid = scope
    }
    if scope == "" {
        return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
    }
    
    // 双轨制权限检查
    var hasPermission bool
    
    if req.ProjectUid != "" && req.ProjectUid != "0" {
        // 新逻辑：检查Project级别的createLot权限
        hasPermission = client.IsAllowed(ctx, "", biz.PermCreateLot, biz.PermClsProject, req.ProjectUid)
    } else {
        // 原有逻辑：检查组织级别的create权限
        scope = client.GroupScope(scope)
        hasPermission = client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsLot, scope)
    }
    
    if !hasPermission {
        return nil, errors.NewErrForbidden()
    }
    
    // ... 其余逻辑保持不变 ...
    lot, err := o.bz.Create(ctx, ToBizLot(req))
    return FromBizLot(lot), err
}
```

**其他Lot相关方法的兼容性改造：**

```go
// 添加权限检查辅助方法
func (o *LotsService) checkLotPermission(ctx context.Context, perm, lotUid string, lot *biz.Lot) bool {
    if lot.ProjectID > 0 {
        // 新逻辑：通过Project检查权限
        projectUid := kid.StringID(lot.ProjectID)
        
        // 先检查Project级别权限
        if client.IsAllowed(ctx, "", perm, biz.PermClsProject, projectUid) {
            return true
        }
        
        // 再检查Lot级别权限（Project下的Lot可能有独立权限）
        return client.IsAllowed(ctx, "", perm, biz.PermClsLot, lotUid)
    } else {
        // 原有逻辑：直接检查Lot权限
        return client.IsAllowed(ctx, "", perm, biz.PermClsLot, lotUid)
    }
}

// 在GetLot、UpdateLot、DeleteLot等方法中使用
func (o *LotsService) GetLot(ctx context.Context, req *anno.GetLotRequest) (*anno.Lot, error) {
    lot, err := o.bz.GetByUid(ctx, req.Uid)
    if err != nil {
        return nil, err
    }
    
    // 使用兼容性权限检查
    if !o.checkLotPermission(ctx, biz.PermGet, req.Uid, lot) {
        return nil, errors.NewErrForbidden()
    }
    
    return FromBizLot(lot), nil
}
```

#### 文件：`anno/internal/biz/lot.go`

**Create方法的权限策略创建改造：**

```go
func (o *LotsBiz) Create(ctx context.Context, p *Lot) (lot *Lot, err error) {
    o.log.Info(ctx, "CreateLot", "lot", p)

    p.State = LotStateUnstart
    err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        lot, err = o.repo.Create(ctx, p)
        if err != nil {
            return err
        }

        // 双轨制权限策略创建
        var parents []string
        if p.ProjectID > 0 {
            // 新逻辑：Lot的父级是Project
            parents = []string{ProjectScope(kid.StringID(p.ProjectID))}
        } else {
            // 原有逻辑：Lot的父级是组织
            parents = []string{client.GroupScope(p.OrgUid)}
        }
        
        err = client.CreateAccessPolicies(ctx, PermClsLot, p.GetUid(),
            []string{client.UserScope(p.CreatorUid)}, parents)
        if err != nil {
            return err
        }
        
        // 执行团队权限授予逻辑保持不变
        err = o.grantLotExecteamAccess(ctx, lot)
        if err != nil {
            return err
        }

        return o.bgtask.SignalEvent(ctx, &common.Event{LotID: lot.ID, Event: common.EvtLotCreated})
    })
    
    return lot, err
}
```

### 2.5 阶段五：Project业务逻辑改造

#### 文件：`anno/internal/service/project.go` (如果存在)

**需要添加权限检查逻辑：**

```go
func (o *ProjectsService) CreateProject(ctx context.Context, req *anno.CreateProjectRequest) (*anno.Project, error) {
    user := biz.UserFromCtx(ctx)
    scope := req.OrgUid
    if scope == "" {
        scope = user.GetOrgUid()
        req.OrgUid = scope
    }
    if scope == "" {
        return nil, errors.NewErrEmptyField(errors.WithFields("org_uid"))
    }
    
    // 检查组织级别的Project创建权限
    scope = client.GroupScope(scope)
    if !client.IsAllowed(ctx, "", biz.PermCreate, biz.PermClsProject, scope) {
        return nil, errors.NewErrForbidden()
    }
    
    project := ToBizProject(req)
    project.CreatorUid = user.GetUid()
    project, err := o.bz.Create(ctx, project)
    return FromBizProject(project), err
}

func (o *ProjectsService) GetProject(ctx context.Context, req *anno.GetProjectRequest) (*anno.Project, error) {
    if !client.IsAllowed(ctx, "", biz.PermGet, biz.PermClsProject, req.Uid) {
        return nil, errors.NewErrForbidden()
    }
    
    project, err := o.bz.GetByUid(ctx, req.Uid)
    if err != nil {
        return nil, err
    }
    return FromBizProject(project), nil
}

func (o *ProjectsService) UpdateProject(ctx context.Context, req *anno.UpdateProjectRequest) (*anno.Project, error) {
    if !client.IsAllowed(ctx, "", biz.PermUpdate, biz.PermClsProject, req.Uid) {
        return nil, errors.NewErrForbidden()
    }
    
    // ... 更新逻辑
}

func (o *ProjectsService) DeleteProject(ctx context.Context, req *anno.DeleteProjectRequest) (*emptypb.Empty, error) {
    if !client.IsAllowed(ctx, "", biz.PermDelete, biz.PermClsProject, req.Uid) {
        return nil, errors.NewErrForbidden()
    }
    
    // ... 删除逻辑
}

func (o *ProjectsService) ListProjects(ctx context.Context, req *anno.ListProjectsRequest) (*anno.ListProjectsResponse, error) {
    // 列表权限通常在组织级别检查
    scope := client.GroupScope(req.OrgUid)
    if !client.IsAllowed(ctx, "", biz.PermList, biz.PermClsProject, scope) {
        return nil, errors.NewErrForbidden()
    }
    
    // ... 列表逻辑
}
```

#### 文件：`anno/internal/biz/project.go` (如果存在)

**需要添加权限策略创建逻辑：**

```go
func (o *ProjectsBiz) Create(ctx context.Context, p *Project) (project *Project, err error) {
    o.log.Info(ctx, "CreateProject", "project", p)

    err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
        project, err = o.repo.Create(ctx, p)
        if err != nil {
            return err
        }

        // 创建Project权限策略
        err = client.CreateAccessPolicies(ctx, PermClsProject, p.GetUid(),
            []string{client.UserScope(p.CreatorUid)},     // 创建者作为owner
            []string{client.GroupScope(p.OrgUid)})        // 所属组织作为parent
        if err != nil {
            return err
        }

        return nil
    })
    
    return project, err
}
```

## 3. 逻辑一致性检查

### 3.1 权限继承链验证

**原有权限链：**
```
Job → Lot → 组织
```

**新权限链：**
```
Job → Lot → Project → 组织  (有Project的情况)
Job → Lot → 组织           (无Project的情况，兼容)
```

**验证点：**
✅ Job权限检查逻辑无需修改（继承Lot权限）
✅ Lot权限检查需要双轨制（支持Project和组织两种父级）
✅ Project权限检查继承组织权限

### 3.2 权限策略创建一致性

**Lot创建时的策略：**
- **有Project**: `parents = [ProjectScope(projectID)]`
- **无Project**: `parents = [GroupScope(orgUID)]`

**Project创建时的策略：**
- **固定模式**: `parents = [GroupScope(orgUID)]`

**Job创建时的策略：**
- **固定模式**: `parents = [LotScope(lotID)]` (无需修改)

### 3.3 命名空间关系验证

**AnnoProject关系：**
- `parents: IamGroup[]` ✅ 正确
- `policies: IamPolicy[]` ✅ 正确

**AnnoLot关系修改：**
- `parents: (IamGroup|AnnoLot|AnnoProject)[]` ✅ 正确，支持三种父级

**AnnoJob关系：**
- `parents: AnnoLot[]` ✅ 无需修改，继承Lot权限

## 4. 兼容性保证

### 4.1 历史数据兼容

**历史Lot数据特征：**
- `project_id = 0` 或 `project_id IS NULL`
- 权限策略父级为组织：`AnnoLot:lotXXX#parents@IamGroup:orgXXX`

**兼容性措施：**
1. **权限检查双轨制**: 根据project_id判断使用哪套逻辑
2. **权限策略保持**: 历史数据的权限策略不变
3. **查询兼容**: 列表查询需要同时支持两种数据

### 4.2 API兼容性

**CreateLot API：**
- `project_uid` 字段可选
- 为空或"0"时使用原有逻辑
- 有值时使用新逻辑

**其他Lot API：**
- 通过数据库中的project_id判断权限检查逻辑
- 对外API无需修改

## 5. 实施步骤和注意事项

### 5.1 实施顺序

1. **阶段1**: 更新Keto静态规则（tuples.txt + namespaces.keto.ts）
2. **阶段2**: 重启Keto服务，验证规则加载
3. **阶段3**: 更新IAM动态规则（object.go）
4. **阶段4**: 更新Anno权限常量（perms.go）
5. **阶段5**: 更新Anno权限检查逻辑（service层 + biz层）
6. **阶段6**: 实现Project业务逻辑权限
7. **阶段7**: 全面测试验证

### 5.2 关键注意事项

#### 数据一致性
- **project_id字段处理**: 确保所有判断都考虑0、NULL、>0三种情况
- **权限策略一致性**: 新创建的Project必须同时创建权限策略
- **父级关系正确性**: Lot的父级关系要根据project_id正确设置

#### 性能考虑
- **权限检查性能**: 双轨制可能增加权限检查次数，需要监控
- **权限规则数量**: 新增Project会增加权限规则数量，注意Keto性能
- **查询性能**: Project-Lot关联查询的性能优化

#### 错误处理
- **权限策略创建失败**: 需要回滚业务数据创建
- **权限检查异常**: 需要有合理的降级策略
- **数据不一致**: 需要有数据修复机制

### 5.3 测试验证清单

#### 功能测试
- [ ] 创建Project并验证权限策略生成
- [ ] 在Project下创建Lot并验证权限继承
- [ ] 历史Lot的权限检查仍然正常
- [ ] Job权限继承链完整
- [ ] 各种角色的权限检查正确

#### 兼容性测试
- [ ] 历史数据（project_id=0）的访问正常
- [ ] 新数据（project_id>0）的访问正常
- [ ] 混合场景下的权限检查正确
- [ ] API向后兼容

#### 性能测试
- [ ] 权限检查性能无明显下降
- [ ] 大量Project/Lot数据下的性能表现
- [ ] Keto权限规则数量对性能的影响

## 6. 风险评估和缓解

### 6.1 主要风险

1. **权限检查逻辑复杂化**: 双轨制增加了代码复杂度
2. **数据不一致风险**: 新老数据的权限策略可能不一致
3. **性能影响**: 可能增加权限检查时间
4. **回滚复杂性**: 如果出现问题，回滚可能比较复杂

### 6.2 缓解措施

1. **充分测试**: 覆盖所有权限检查场景和边界情况
2. **渐进式部署**: 先在测试环境验证，再逐步推广
3. **监控告警**: 监控权限检查性能和错误率
4. **回滚预案**: 准备快速回滚的方案和脚本
5. **文档完善**: 详细记录新的权限检查逻辑和故障排查方法

## 7. 总结

### 7.1 方案优势

- **最小化风险**: 双轨制设计保证历史数据完全兼容
- **架构一致**: 复用现有的权限设计模式，保持系统一致性
- **功能完整**: 支持完整的Project层级权限管理
- **可扩展**: 为未来的多层级权限扩展奠定基础

### 7.2 实施建议

1. **分阶段实施**: 按照建议的步骤逐步实施，每个阶段都要充分测试
2. **重点关注兼容性**: 确保历史数据的访问不受影响
3. **性能监控**: 密切关注权限检查性能的变化
4. **文档更新**: 及时更新权限系统的设计文档和使用说明

这个方案在保证功能完整性的同时，最大化地降低了实施风险，是一个可行且稳妥的改造方案。

#### Project层级权限检查逻辑对比

```mermaid
graph TB
    subgraph "原有权限检查逻辑"
        A1[用户请求访问Lot] --> B1{检查Lot权限}
        B1 -->|有权限| C1[允许访问]
        B1 -->|无权限| D1{检查组织权限}
        D1 -->|有权限| C1
        D1 -->|无权限| E1[拒绝访问]
    end
    
    subgraph "新的双轨制权限检查逻辑"
        A2[用户请求访问Lot] --> B2{检查Lot的project_id}
        
        B2 -->|project_id > 0| C2[新逻辑: 三层检查]
        B2 -->|project_id = 0| D2[原有逻辑: 二层检查]
        
        C2 --> E2{检查Project权限}
        E2 -->|有权限| F2[允许访问]
        E2 -->|无权限| G2{检查Lot权限}
        G2 -->|有权限| F2
        G2 -->|无权限| H2{检查组织权限<br/>通过Project}
        H2 -->|有权限| F2
        H2 -->|无权限| I2[拒绝访问]
        
        D2 --> J2{检查Lot权限}
        J2 -->|有权限| F2
        J2 -->|无权限| K2{检查组织权限<br/>直接关联}
        K2 -->|有权限| F2
        K2 -->|无权限| I2
    end
    
    subgraph "Job权限继承 (无需修改)"
        A3[用户请求访问Job] --> B3{检查Job权限}
        B3 -->|无权限| C3{检查父级Lot权限}
        C3 --> D3[使用上述Lot权限检查逻辑]
        D3 -->|有权限| E3[允许访问]
        D3 -->|无权限| F3[拒绝访问]
    end
    
    style C2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style D2 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style F2 fill:#c8e6c9
    style I2 fill:#ffcdd2
    style E3 fill:#c8e6c9
    style F3 fill:#ffcdd2
```