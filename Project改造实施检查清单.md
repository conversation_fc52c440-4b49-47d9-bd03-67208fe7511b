# Project改造实施检查清单

## 📋 实施前检查

### 🔍 代码审查检查
- [ ] **权限常量定义完整性**
  - [ ] `PermClsProject = "AnnoProject"` 已定义
  - [ ] Project特有权限常量已添加（createLot, listLot, manageMember）
  - [ ] `ProjectScope(uid)` 函数已实现

- [ ] **静态规则完整性**
  - [ ] tuples.txt中AnnoProject角色权限定义完整
  - [ ] Project权限继承到Lot的规则正确
  - [ ] 复合角色集成Project权限的规则正确
  - [ ] 全局策略应用到AnnoProject的规则正确

- [ ] **命名空间定义正确性**
  - [ ] AnnoProject命名空间定义正确
  - [ ] AnnoLot命名空间支持AnnoProject父级
  - [ ] 权限检查逻辑实现正确

### 🔧 IAM层检查
- [ ] **Object.go改造**
  - [ ] ProjectNs常量已定义
  - [ ] MakeProjectName函数已实现
  - [ ] ProjectScope函数已实现
  - [ ] CreateProjectAccessPolicies方法已实现（可选）

### 🏗️ Anno层检查
- [ ] **权限检查双轨制实现**
  - [ ] CreateLot方法的双轨制权限检查
  - [ ] checkLotPermission辅助方法实现
  - [ ] 其他Lot相关方法使用checkLotPermission

- [ ] **权限策略创建双轨制**
  - [ ] LotsBiz.Create方法的双轨制策略创建
  - [ ] Project存在时父级设置为Project
  - [ ] Project不存在时父级设置为组织

- [ ] **Project业务逻辑**
  - [ ] ProjectsService各方法的权限检查
  - [ ] ProjectsBiz.Create方法的权限策略创建

## 🧪 测试验证检查

### ✅ 功能测试
- [ ] **Project CRUD测试**
  - [ ] 创建Project并验证权限策略生成
  - [ ] 查看Project权限检查正确
  - [ ] 更新Project权限检查正确
  - [ ] 删除Project权限检查正确
  - [ ] 列表Project权限检查正确

- [ ] **Lot权限继承测试**
  - [ ] 在Project下创建Lot，验证权限继承
  - [ ] Project owner可以访问下属Lot
  - [ ] Project editor可以访问下属Lot
  - [ ] Project viewer只能查看下属Lot

- [ ] **Job权限继承测试**
  - [ ] Job继承Lot权限正常
  - [ ] 通过Project权限可以访问Job
  - [ ] Job权限检查路径：Job→Lot→Project→组织

### 🔄 兼容性测试
- [ ] **历史数据兼容**
  - [ ] project_id=0的历史Lot访问正常
  - [ ] 历史Lot的权限检查使用原有逻辑
  - [ ] 历史Job的权限继承正常

- [ ] **混合场景测试**
  - [ ] 同一组织下有Project和无Project的Lot并存
  - [ ] 权限检查逻辑正确区分
  - [ ] 用户权限在两种场景下都正确

- [ ] **API兼容性测试**
  - [ ] CreateLot API的project_uid字段可选
  - [ ] 其他API无需修改即可正常工作

### 🚀 性能测试
- [ ] **权限检查性能**
  - [ ] 双轨制权限检查性能无明显下降
  - [ ] 大量Project/Lot数据下的性能表现
  - [ ] 权限检查响应时间在可接受范围内

- [ ] **Keto性能**
  - [ ] 新增权限规则对Keto性能的影响
  - [ ] 权限规则数量增长的性能影响
  - [ ] 权限查询的响应时间

## 🔒 安全性检查

### 🛡️ 权限隔离验证
- [ ] **Project级权限隔离**
  - [ ] 不同Project的用户无法互相访问
  - [ ] Project权限不会泄露到其他Project
  - [ ] 组织级权限仍然有效

- [ ] **角色权限验证**
  - [ ] Project.viewer只能查看，不能修改
  - [ ] Project.editor可以修改，不能删除
  - [ ] Project.owner拥有完整权限

- [ ] **权限继承验证**
  - [ ] 组织权限正确继承到Project
  - [ ] Project权限正确继承到Lot
  - [ ] Lot权限正确继承到Job

## 📊 数据一致性检查

### 🔍 权限策略一致性
- [ ] **新创建资源**
  - [ ] Project创建时权限策略正确生成
  - [ ] Lot创建时根据project_id正确设置父级
  - [ ] Job创建时权限策略继承Lot

- [ ] **权限策略完整性**
  - [ ] 每个Project都有对应的权限策略
  - [ ] 权限策略的父级关系正确
  - [ ] 权限策略的所有者设置正确

### 🔗 关系一致性
- [ ] **父级关系正确性**
  - [ ] Project的父级是组织
  - [ ] 有Project的Lot父级是Project
  - [ ] 无Project的Lot父级是组织
  - [ ] Job的父级是Lot

## 🚨 错误处理检查

### ⚠️ 异常场景处理
- [ ] **权限策略创建失败**
  - [ ] 业务数据创建回滚正确
  - [ ] 错误信息清晰明确
  - [ ] 不会产生数据不一致

- [ ] **权限检查异常**
  - [ ] 权限检查失败时的降级策略
  - [ ] 错误日志记录完整
  - [ ] 用户体验友好

- [ ] **数据不一致处理**
  - [ ] 检测数据不一致的机制
  - [ ] 数据修复的工具和流程
  - [ ] 预防数据不一致的措施

## 📈 监控和告警检查

### 📊 关键指标监控
- [ ] **权限检查性能指标**
  - [ ] 权限检查平均响应时间
  - [ ] 权限检查成功率
  - [ ] 权限检查错误率

- [ ] **业务指标监控**
  - [ ] Project创建成功率
  - [ ] Lot创建成功率（双轨制）
  - [ ] 权限策略创建成功率

### 🚨 告警配置
- [ ] **性能告警**
  - [ ] 权限检查响应时间超阈值告警
  - [ ] Keto服务性能异常告警
  - [ ] 数据库查询性能异常告警

- [ ] **错误告警**
  - [ ] 权限策略创建失败告警
  - [ ] 权限检查异常告警
  - [ ] 数据不一致告警

## 📚 文档和培训检查

### 📖 文档更新
- [ ] **技术文档**
  - [ ] 权限系统架构文档更新
  - [ ] API文档更新（如有变化）
  - [ ] 故障排查文档更新

- [ ] **运维文档**
  - [ ] 部署文档更新
  - [ ] 监控配置文档
  - [ ] 回滚操作文档

### 👥 团队培训
- [ ] **开发团队**
  - [ ] 新权限模型的理解
  - [ ] 双轨制实现的原理
  - [ ] 常见问题的排查方法

- [ ] **运维团队**
  - [ ] 新增监控指标的含义
  - [ ] 异常情况的处理流程
  - [ ] 性能优化的方法

## 🔄 回滚准备检查

### 🛠️ 回滚方案
- [ ] **代码回滚**
  - [ ] 代码版本管理清晰
  - [ ] 回滚脚本准备完毕
  - [ ] 回滚影响评估完成

- [ ] **数据回滚**
  - [ ] 权限规则备份
  - [ ] 数据库变更备份
  - [ ] 回滚数据验证方案

### ⏱️ 回滚时机
- [ ] **回滚触发条件**
  - [ ] 性能下降超过阈值
  - [ ] 错误率超过阈值
  - [ ] 功能异常无法快速修复

- [ ] **回滚执行**
  - [ ] 回滚操作步骤清晰
  - [ ] 回滚验证方法明确
  - [ ] 回滚后的监控计划

## ✅ 上线准备检查

### 🚀 部署准备
- [ ] **环境准备**
  - [ ] 测试环境验证完成
  - [ ] 预生产环境验证完成
  - [ ] 生产环境部署计划确认

- [ ] **发布计划**
  - [ ] 发布时间窗口确认
  - [ ] 发布步骤详细规划
  - [ ] 发布后验证计划

### 👥 团队准备
- [ ] **值班安排**
  - [ ] 发布期间值班人员安排
  - [ ] 紧急联系方式确认
  - [ ] 问题升级流程明确

这个检查清单确保了Project层级改造的全面性和可靠性，建议在实施过程中逐项检查，确保每个环节都得到充分验证。
